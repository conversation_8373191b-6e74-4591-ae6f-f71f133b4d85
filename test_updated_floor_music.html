<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Updated Floor Music Test</title>
</head>
<body>
    <h1>Updated Floor-Based Music Test</h1>
    <div>
        <button onclick="testFloorMusic(1)">Test Floor 1 Music</button>
        <button onclick="testFloorMusic(2)">Test Floor 2 Music</button>
        <button onclick="testFloorMusic(3)">Test Floor 3 Music</button>
        <button onclick="testFloorMusic(4)">Test Floor 4 Music</button>
        <button onclick="testFloorMusic(5)">Test Floor 5 Music</button>
        <button onclick="testFloorMusic(6)">Test Floor 6 Music</button>
        <button onclick="testFloorMusic(7)">Test Floor 7 Music</button>
        <button onclick="testFloorMusic(8)">Test Floor 8 Music</button>
        <button onclick="testFloorMusic(9)">Test Floor 9 Music</button>
        <button onclick="testFloorMusic(10)">Test Floor 10 Music</button>
    </div>
    <div id="output"></div>

    <script src="js/core/Constants.js"></script>
    <script src="js/core/GameState.js"></script>
    <script src="js/systems/SoundManager.js"></script>
    
    <script>
        // Initialize gameState for testing
        const gameState = new GameState();
        
        function testFloorMusic(floor) {
            const output = document.getElementById('output');
            
            // Test the floor-based music path selection
            const musicPath = soundManager.getFloorBasedMusicPath(floor);
            
            output.innerHTML += `<p><strong>Floor ${floor}:</strong> ${musicPath}</p>`;
            
            console.log(`Floor ${floor} music path: ${musicPath}`);
        }
        
        // Test all floors on load
        window.onload = function() {
            console.log('Testing updated floor-based music selection...');
            
            // Clear output
            document.getElementById('output').innerHTML = '<h2>Music Selection Results:</h2>';
            
            for (let i = 1; i <= 10; i++) {
                testFloorMusic(i);
            }
            
            // Add summary
            document.getElementById('output').innerHTML += `
                <h3>Expected Results:</h3>
                <ul>
                    <li>Floors 1-3: "The Fall of Love.m4a"</li>
                    <li>Floors 4-6: "Notturno.m4a"</li>
                    <li>Floors 7-10: "Profundo.m4a"</li>
                </ul>
            `;
        };
    </script>
</body>
</html>
